import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";
import * as api from "../../../api";
import * as useMobileHook from "../../../hooks/useMobile";
import PhoneLogin from "../index";
import {
  createApiError,
  createMockApiResponse,
  createMockProps,
  createMockUser,
  mockConsole,
  TEST_SCENARIOS
} from "./test-utils";

// Mock API functions
vi.mock("../../../api", () => ({
  fetchStartVerify: vi.fn(),
  fetchLogin: vi.fn()
}));

// Mock useMobile hook
vi.mock("../../../hooks/useMobile", () => ({
  useMobile: vi.fn()
}));

// Mock CSS imports
vi.mock("../index.css", () => ({}));

describe("PhoneLogin API Integration", () => {
  const mockProps = createMockProps();

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(useMobileHook.useMobile).mockReturnValue(false);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe("fetchStartVerify API", () => {
    it("should call fetchStartVerify with correct parameters", async () => {
      const user = userEvent.setup();
      const { phoneNumber, verificationCode, apiResponse } =
        TEST_SCENARIOS.successfulLogin;

      vi.mocked(api.fetchStartVerify).mockResolvedValue(apiResponse);
      vi.mocked(api.fetchLogin).mockResolvedValue(
        createMockApiResponse.fetchLogin()
      );

      render(<PhoneLogin {...mockProps} />);

      // Complete the login flow
      const verifyButton = screen.getByText("点击按钮开始智能验证");
      await user.click(verifyButton);

      await waitFor(() => {
        expect(screen.getByPlaceholderText("短信验证码")).toBeInTheDocument();
      });

      const phoneInput = screen.getByPlaceholderText("手机号码");
      const codeInput = screen.getByPlaceholderText("短信验证码");

      await user.type(phoneInput, phoneNumber);
      await user.type(codeInput, verificationCode);

      const loginButton = screen.getByText("立即登录/注册");
      await user.click(loginButton);

      // Verify API call
      await waitFor(() => {
        expect(api.fetchStartVerify).toHaveBeenCalledWith(
          phoneNumber,
          verificationCode
        );
      });
    });

    it("should handle fetchStartVerify API errors", async () => {
      const user = userEvent.setup();
      const { mocks, restore } = mockConsole();
      const { phoneNumber, verificationCode, error } = TEST_SCENARIOS.apiError;

      vi.mocked(api.fetchStartVerify).mockRejectedValue(error);

      render(<PhoneLogin {...mockProps} />);

      // Complete the login flow
      const verifyButton = screen.getByText("点击按钮开始智能验证");
      await user.click(verifyButton);

      await waitFor(() => {
        expect(screen.getByPlaceholderText("短信验证码")).toBeInTheDocument();
      });

      const phoneInput = screen.getByPlaceholderText("手机号码");
      const codeInput = screen.getByPlaceholderText("短信验证码");

      await user.type(phoneInput, phoneNumber);
      await user.type(codeInput, verificationCode);

      const loginButton = screen.getByText("立即登录/注册");
      await user.click(loginButton);

      // Should handle error gracefully
      await waitFor(() => {
        expect(mocks.warn).toHaveBeenCalled();
      });

      // Should not call onLoginSuccess
      expect(mockProps.onLoginSuccess).not.toHaveBeenCalled();

      restore();
    });

    it("should handle different API response formats", async () => {
      const user = userEvent.setup();

      // Test with different response format
      const customResponse = createMockApiResponse.fetchStartVerify({
        result: "custom_success",
        ssid: "custom-ssid-456",
        additionalData: "test"
      });

      vi.mocked(api.fetchStartVerify).mockResolvedValue(customResponse);
      vi.mocked(api.fetchLogin).mockResolvedValue(
        createMockApiResponse.fetchLogin()
      );

      render(<PhoneLogin {...mockProps} />);

      // Complete login flow
      const verifyButton = screen.getByText("点击按钮开始智能验证");
      await user.click(verifyButton);

      await waitFor(() => {
        expect(screen.getByPlaceholderText("短信验证码")).toBeInTheDocument();
      });

      const phoneInput = screen.getByPlaceholderText("手机号码");
      const codeInput = screen.getByPlaceholderText("短信验证码");

      await user.type(phoneInput, "13800138000");
      await user.type(codeInput, "123456");

      const loginButton = screen.getByText("立即登录/注册");
      await user.click(loginButton);

      // Should still work with custom response format
      await waitFor(() => {
        expect(api.fetchLogin).toHaveBeenCalled();
      });
    });
  });

  describe("fetchLogin API", () => {
    beforeEach(() => {
      // Setup successful fetchStartVerify for all fetchLogin tests
      vi.mocked(api.fetchStartVerify).mockResolvedValue(
        createMockApiResponse.fetchStartVerify()
      );
    });

    it("should call fetchLogin with correct parameters", async () => {
      const user = userEvent.setup();
      const mockUsers = [
        createMockUser(),
        createMockUser({ userid: 2, nickname: "User 2" })
      ];

      vi.mocked(api.fetchLogin).mockResolvedValue(
        createMockApiResponse.fetchLogin(mockUsers)
      );

      render(<PhoneLogin {...mockProps} />);

      // Complete login flow
      const verifyButton = screen.getByText("点击按钮开始智能验证");
      await user.click(verifyButton);

      await waitFor(() => {
        expect(screen.getByPlaceholderText("短信验证码")).toBeInTheDocument();
      });

      const phoneInput = screen.getByPlaceholderText("手机号码");
      const codeInput = screen.getByPlaceholderText("短信验证码");

      await user.type(phoneInput, "13800138000");
      await user.type(codeInput, "123456");

      const loginButton = screen.getByText("立即登录/注册");
      await user.click(loginButton);

      // Verify fetchLogin is called with correct parameters
      await waitFor(() => {
        expect(api.fetchLogin).toHaveBeenCalledWith(
          expect.objectContaining({
            ssid: "test-ssid-123",
            filter_rule: expect.any(String),
            check_rule: expect.any(String),
            _: expect.any(String)
          })
        );
      });

      // Verify success callback with users
      await waitFor(() => {
        expect(mockProps.onLoginSuccess).toHaveBeenCalledWith(mockUsers);
      });
    });

    it("should handle fetchLogin API errors", async () => {
      const user = userEvent.setup();
      const { mocks, restore } = mockConsole();

      vi.mocked(api.fetchLogin).mockRejectedValue(
        createApiError("Login API Error")
      );

      render(<PhoneLogin {...mockProps} />);

      // Complete login flow
      const verifyButton = screen.getByText("点击按钮开始智能验证");
      await user.click(verifyButton);

      await waitFor(() => {
        expect(screen.getByPlaceholderText("短信验证码")).toBeInTheDocument();
      });

      const phoneInput = screen.getByPlaceholderText("手机号码");
      const codeInput = screen.getByPlaceholderText("短信验证码");

      await user.type(phoneInput, "13800138000");
      await user.type(codeInput, "123456");

      const loginButton = screen.getByText("立即登录/注册");
      await user.click(loginButton);

      // Should handle error gracefully
      await waitFor(() => {
        expect(mocks.warn).toHaveBeenCalled();
      });

      // Should not call success callback
      expect(mockProps.onLoginSuccess).not.toHaveBeenCalled();

      restore();
    });

    it("should handle empty users response", async () => {
      const user = userEvent.setup();

      vi.mocked(api.fetchLogin).mockResolvedValue(
        createMockApiResponse.fetchLogin([])
      );

      render(<PhoneLogin {...mockProps} />);

      // Complete login flow
      const verifyButton = screen.getByText("点击按钮开始智能验证");
      await user.click(verifyButton);

      await waitFor(() => {
        expect(screen.getByPlaceholderText("短信验证码")).toBeInTheDocument();
      });

      const phoneInput = screen.getByPlaceholderText("手机号码");
      const codeInput = screen.getByPlaceholderText("短信验证码");

      await user.type(phoneInput, "13800138000");
      await user.type(codeInput, "123456");

      const loginButton = screen.getByText("立即登录/注册");
      await user.click(loginButton);

      await waitFor(() => {
        expect(mockProps.onLoginSuccess).toHaveBeenCalledWith([]);
      });
    });
  });

  describe("API Call Sequence", () => {
    it("should call APIs in correct sequence", async () => {
      const user = userEvent.setup();
      const callOrder: string[] = [];

      vi.mocked(api.fetchStartVerify).mockImplementation(async () => {
        callOrder.push("fetchStartVerify");
        return createMockApiResponse.fetchStartVerify();
      });

      vi.mocked(api.fetchLogin).mockImplementation(async () => {
        callOrder.push("fetchLogin");
        return createMockApiResponse.fetchLogin();
      });

      render(<PhoneLogin {...mockProps} />);

      // Complete login flow
      const verifyButton = screen.getByText("点击按钮开始智能验证");
      await user.click(verifyButton);

      await waitFor(() => {
        expect(screen.getByPlaceholderText("短信验证码")).toBeInTheDocument();
      });

      const phoneInput = screen.getByPlaceholderText("手机号码");
      const codeInput = screen.getByPlaceholderText("短信验证码");

      await user.type(phoneInput, "13800138000");
      await user.type(codeInput, "123456");

      const loginButton = screen.getByText("立即登录/注册");
      await user.click(loginButton);

      // Verify correct call sequence
      await waitFor(() => {
        expect(callOrder).toEqual(["fetchStartVerify", "fetchLogin"]);
      });
    });

    it("should not call fetchLogin if fetchStartVerify fails", async () => {
      const user = userEvent.setup();
      const { mocks, restore } = mockConsole();

      vi.mocked(api.fetchStartVerify).mockRejectedValue(
        createApiError("Verify failed")
      );

      render(<PhoneLogin {...mockProps} />);

      // Complete login flow
      const verifyButton = screen.getByText("点击按钮开始智能验证");
      await user.click(verifyButton);

      await waitFor(() => {
        expect(screen.getByPlaceholderText("短信验证码")).toBeInTheDocument();
      });

      const phoneInput = screen.getByPlaceholderText("手机号码");
      const codeInput = screen.getByPlaceholderText("短信验证码");

      await user.type(phoneInput, "13800138000");
      await user.type(codeInput, "123456");

      const loginButton = screen.getByText("立即登录/注册");
      await user.click(loginButton);

      // Should not call fetchLogin
      await waitFor(() => {
        expect(mocks.warn).toHaveBeenCalled();
      });

      expect(api.fetchLogin).not.toHaveBeenCalled();

      restore();
    });
  });
});
