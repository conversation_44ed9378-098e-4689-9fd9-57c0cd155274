import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { beforeEach, describe, expect, it, vi } from "vitest";
import * as api from "../../../api";
import * as useMobileHook from "../../../hooks/useMobile";
import PhoneLogin from "../index";

vi.mock("../../../api", () => ({
  fetchStartVerify: vi.fn(),
  fetchLogin: vi.fn()
}));

vi.mock("../../../hooks/useMobile", () => ({
  useMobile: vi.fn()
}));

vi.mock("../index.css", () => ({}));

describe("手机登录组件", () => {
  const mockProps = {
    onBack: vi.fn(),
    onLoginSuccess: vi.fn(),
    onCurrentBtn: vi.fn()
  };

  const mockUser = {
    userid: 1,
    nickname: "测试用户",
    company_id: 1,
    company_name: "测试公司",
    avatar_url: "",
    is_current: true,
    loginmode: "phone"
  };

  beforeEach(() => {
    vi.clearAllMocks();
    // 默认桌面端
    vi.mocked(useMobileHook.useMobile).mockReturnValue(false);

    // Mock API 成功响应
    vi.mocked(api.fetchStartVerify).mockResolvedValue({
      result: "success",
      ssid: "test-ssid-123"
    });

    vi.mocked(api.fetchLogin).mockResolvedValue({
      users: [mockUser]
    });
  });

  it("桌面端应该显示返回按钮", () => {
    vi.mocked(useMobileHook.useMobile).mockReturnValue(false);
    render(<PhoneLogin {...mockProps} />);

    expect(screen.getByText("返回")).toBeInTheDocument();
    expect(screen.getByText("短信验证码登录")).toBeInTheDocument();
  });

  it("移动端不应该显示返回按钮", () => {
    vi.mocked(useMobileHook.useMobile).mockReturnValue(true);
    render(<PhoneLogin {...mockProps} />);

    expect(screen.queryByText("返回")).not.toBeInTheDocument();
  });

  it("应该允许用户输入验证码", async () => {
    const user = userEvent.setup();
    render(<PhoneLogin {...mockProps} />);

    // 假装智能验证通过
    const verifyButton = screen.getByText("点击按钮开始智能验证");
    await user.click(verifyButton);

    await waitFor(() => {
      expect(screen.getByPlaceholderText("短信验证码")).toBeInTheDocument();
    });

    // 输入验证码
    const codeInput = screen.getByPlaceholderText("短信验证码");
    await user.type(codeInput, "123456");
    expect(codeInput).toHaveValue("123456");
  });

  it("点击发送验证码应该显示倒计时", async () => {
    const user = userEvent.setup();
    render(<PhoneLogin {...mockProps} />);

    // 假装智能验证通过
    const verifyButton = screen.getByText("点击按钮开始智能验证");
    await user.click(verifyButton);

    await waitFor(() => {
      expect(screen.getByText("发送验证码")).toBeInTheDocument();
    });

    // 输入手机号
    const phoneInput = screen.getByPlaceholderText("手机号码");
    await user.type(phoneInput, "13800138000");

    // 点击发送验证码
    const sendCodeButton = screen.getByText("发送验证码");
    await user.click(sendCodeButton);

    // 应该显示倒计时
    await waitFor(() => {
      expect(screen.getByText(/重发\(\d+s\)/)).toBeInTheDocument();
    });
  });

  it("没有手机号时应该显示错误提示", async () => {
    const user = userEvent.setup();
    render(<PhoneLogin {...mockProps} />);

    // 直接点击登录按钮，不进行智能验证
    const loginButton = screen.getByText("立即登录/注册");
    await user.click(loginButton);

    // 应该提示输入手机号码
    await waitFor(() => {
      expect(screen.getByText("请输入手机号码")).toBeInTheDocument();
    });
  });

  it("有手机号但没有验证码时应该显示验证码错误提示", async () => {
    const user = userEvent.setup();
    render(<PhoneLogin {...mockProps} />);

    // 假装智能验证通过
    const verifyButton = screen.getByText("点击按钮开始智能验证");
    await user.click(verifyButton);

    await waitFor(() => {
      expect(screen.getByPlaceholderText("短信验证码")).toBeInTheDocument();
    });

    // 只输入手机号，不输入验证码
    const phoneInput = screen.getByPlaceholderText("手机号码");
    await user.type(phoneInput, "13800138000");

    // 点击登录
    const loginButton = screen.getByText("立即登录/注册");
    await user.click(loginButton);

    // 应该提示输入验证码
    await waitFor(() => {
      expect(screen.getByText("请输入验证码")).toBeInTheDocument();
    });
  });

  it("验证码输入错误时应该显示错误提示", async () => {
    const user = userEvent.setup();
    // Mock API 返回验证码错误
    vi.mocked(api.fetchStartVerify).mockRejectedValue(new Error("验证码错误"));

    render(<PhoneLogin {...mockProps} />);

    // 先点击智能验证按钮显示验证码输入框
    const verifyButton = screen.getByText("点击按钮开始智能验证");
    await user.click(verifyButton);

    await waitFor(() => {
      expect(screen.getByPlaceholderText("短信验证码")).toBeInTheDocument();
    });

    // 输入手机号和错误的验证码
    const phoneInput = screen.getByPlaceholderText("手机号码");
    const codeInput = screen.getByPlaceholderText("短信验证码");

    await user.type(phoneInput, "13800138000");
    await user.type(codeInput, "000000"); // 错误的验证码

    // 点击登录
    const loginButton = screen.getByText("立即登录/注册");
    await user.click(loginButton);

    // 应该显示验证码错误提示
    await waitFor(() => {
      expect(screen.getByText("验证码错误，请重新输入")).toBeInTheDocument();
    });
  });

  // it("手机号格式错误时应该显示错误提示", async () => {
  //   const user = userEvent.setup();
  //   render(<PhoneLogin {...mockProps} />);

  //   // 输入错误格式的手机号
  //   const phoneInput = screen.getByPlaceholderText("手机号码");
  //   await user.type(phoneInput, "123"); // 错误的手机号格式

  //   expect(screen.getByText("请输入正确的手机号码")).toBeInTheDocument();
  // });

  it("点击返回按钮应该调用回调函数", async () => {
    const user = userEvent.setup();
    vi.mocked(useMobileHook.useMobile).mockReturnValue(false); // 桌面端

    render(<PhoneLogin {...mockProps} />);

    const backButton = screen.getByText("返回");
    await user.click(backButton);

    expect(mockProps.onBack).toHaveBeenCalled();
  });
});
