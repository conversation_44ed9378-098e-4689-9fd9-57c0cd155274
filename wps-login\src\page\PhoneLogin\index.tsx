import { useEffect, useState } from "react";
import { fetchLogin, fetchStartVerify } from "../../api";
import Common from "../../components/Common";
import IconButtonGroup from "../../components/IconButtonGroup";
import { loginMethodConfigs } from "../../components/IconButtonGroup/loginIcons";
import { type LoginMethod } from "../../constants";
import { useMobile } from "../../hooks/useMobile";
import type { User, UsersData } from "../../type";
import { isValidInternationalPhone } from "../../utils/phone";
import "./index.css";
interface PhoneLoginProps {
  onBack: () => void;
  onLoginSuccess: (users: User[]) => void;
  onCurrentBtn?: (value: LoginMethod) => void;
}

function PhoneLogin({ onBack, onLoginSuccess, onCurrentBtn }: PhoneLoginProps) {
  //验证按钮
  const [isVerified, setIsVerified] = useState(false);
  const [countryCode, setCountryCode] = useState("+86");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [verificationCode, setVerificationCode] = useState("");
  //验证码--假设点击就是成功的
  const [isCodeVerified, setIsCodeVerified] = useState(false);
  const [count, setCount] = useState(60);
  const [isLoading, setIsLoading] = useState(false);
  // 错误提示状态
  const [errorMessage, setErrorMessage] = useState("");
  // 使用自定义hook检测是否是移动端
  const isMobile = useMobile();

  const handleCurrentBtn = (value: LoginMethod) => {
    if (onCurrentBtn) {
      onCurrentBtn(value);
    }
  };

  const handleSmartVerify = async () => {
    setIsLoading(true);
    try {
      // const result = await fetchStartVerify()
      console.log("验证结果");
      // 设置验证结果
      setIsVerified(true);
    } catch (error) {
      console.error("验证失败", error);
      setIsVerified(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendCode = async () => {
    // 清除之前的错误信息
    setErrorMessage("");

    if (!phoneNumber.trim()) {
      setErrorMessage("请输入手机号码");
      return;
    }
    try {
      // const result = await fetchMessagePhone()
      //直接假设点击验证码就是发送成功的，然后按钮信息变成重发(60)
      //虽然假装发，但是应该也要先判断手机号格式是否正确，正确的手机号才可以发验证码
      const entireNumber = countryCode + phoneNumber;
      if (!isValidInternationalPhone(entireNumber)) {
        console.log("phoneNumber", entireNumber);
        setErrorMessage("请输入正确的手机号码");
        return;
      }
      setIsCodeVerified(true);
      //重置重发倒计时
      setCount(60);
      console.log("发送验证码的手机号:", phoneNumber);
    } catch (err) {
      console.error("验证码发送失败", err);
    }
  };

  useEffect(() => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let timer: any;
    if (isCodeVerified && count > 0) {
      timer = setInterval(() => {
        setCount((prev) => prev - 1);
      }, 1000);
    } else if (count === 0) {
      setIsCodeVerified(false);
    }
    return () => clearInterval(timer);
  }, [isCodeVerified, count]);

  const handleLogin = async () => {
    // 清除之前的错误信息
    setErrorMessage("");

    //提交前就验证信息
    if (!phoneNumber.trim()) {
      setErrorMessage("请输入手机号码");
      return;
    }
    if (!verificationCode.trim()) {
      setErrorMessage("请输入验证码");
      return;
    }
    //发送验证码后，点击立即登录会先调用verify得到ssid，这个ssid再传到后面登录请求中
    //需要传入手机号和验证码参数
    console.log("手机号和验证码", phoneNumber, verificationCode);
    const entireNumber = countryCode + phoneNumber;
    const vres = await fetchStartVerify(entireNumber, verificationCode);
    const params = {
      ssid: vres.ssid!,
      filter_rule: "normal",
      check_rule: "second_phone",
      _: phoneNumber
    };
    console.log("fectlogin参数", params);
    try {
      console.log("准备登录");
      const res = (await fetchLogin(params)) as UsersData;
      console.log("🚀 ~~ handleLogin ~~ res 🤖--EndLog--🤖", res.users);

      // 调用回调函数，切换到UsersList页面
      onLoginSuccess(res.users);
    } catch (err) {
      console.warn(err);
    }
    console.log("登录信息:", { phoneNumber, verificationCode });
  };

  return (
    <Common
      isShowBack={!isMobile}
      onBack={onBack}
      title={isMobile ? "使用金山办公在线服务账号登录" : "短信验证码登录"}
      subTitle={isMobile ? "" : "使用金山办公在线服务账号登录"}
      fchildren={
        <div className="login_footer">
          <p>未注册的手机号验证后将自动创建金山账号</p>
        </div>
      }
    >
      <div className="phone_login_section">
        <div className="phone_input_group">
          <div className="country_code_select">
            <select
              className="country_code"
              value={countryCode}
              onChange={(e) => setCountryCode(e.target.value)}
            >
              <option value="+86">+86</option>
              <option value="+1">+1</option>
              <option value="+44">+44</option>
            </select>
          </div>
          <input
            type="tel"
            className="phone_input"
            placeholder="手机号码"
            value={phoneNumber}
            onChange={(e) => setPhoneNumber(e.target.value)}
            onFocus={() => setErrorMessage("")}
          />
        </div>
        {!isVerified ? (
          <div className="smart_verify_section">
            <div className="verify_button_container">
              <button
                className="smart_verify_btn"
                onClick={handleSmartVerify}
                disabled={isLoading}
              >
                <span className="verify_icon scale ">○</span>
                <span className="verify_text">
                  {isLoading ? "验证中..." : "点击按钮开始智能验证"}
                </span>
              </button>
            </div>
          </div>
        ) : (
          <div className="verification_group">
            <input
              type="text"
              className="verification_input"
              placeholder="短信验证码"
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value)}
              onFocus={() => setErrorMessage("")}
            />
            {!isCodeVerified ? (
              <button className="send_code_btn" onClick={handleSendCode}>
                {count === 0 ? "重新发送验证码" : " 发送验证码 "}
              </button>
            ) : (
              <button
                className="send_code_btn send_code_count"
                disabled={count > 0}
                onClick={handleSendCode}
              >
                {count > 0 ? `重发(${count}s)` : "重新发送验证码"}
              </button>
            )}
          </div>
        )}
        {isMobile ? (
          <div className="auto_login_section">
            <div className="checkbox-div">
              <label className="checkbox-item">
                <input type="checkbox" />
                <span>自动登录</span>
              </label>
            </div>
            {(errorMessage || true) && (
              <div className="error_message">{errorMessage}</div>
            )}
          </div>
        ) : (
          errorMessage && <div className="error_message">{errorMessage}</div>
        )}

        <button className="login_submit_btn" onClick={handleLogin}>
          立即登录/注册
        </button>

        {isMobile && (
          <div className="protocol_section">
            <div className="checkbox-div">
              <label className="checkbox-item">
                <input type="checkbox" />
                <span>
                  已阅读并同意
                  <a href="#" className="protocol_link">
                    隐私保护政策
                  </a>
                  和
                  <a href="#" className="protocol_link">
                    在线服务协议
                  </a>
                </span>
              </label>
            </div>
          </div>
        )}

        {isMobile && (
          <IconButtonGroup
            dividerText="或"
            buttons={[
              {
                ...loginMethodConfigs.wechat,
                onClick: (id) => handleCurrentBtn(id as LoginMethod)
              },
              {
                ...loginMethodConfigs.qq,
                onClick: (id) => handleCurrentBtn(id as LoginMethod)
              },
              {
                ...loginMethodConfigs.sso,
                onClick: (id) => handleCurrentBtn(id as LoginMethod)
              },
              {
                ...loginMethodConfigs.apple,
                onClick: (id) => handleCurrentBtn(id as LoginMethod)
              },
              {
                ...loginMethodConfigs.more,
                onClick: (id) => handleCurrentBtn(id as LoginMethod)
              }
            ]}
            mobileColumns={5}
            desktopColumns={6}
          />
        )}
      </div>
    </Common>
  );
}

export default PhoneLogin;
